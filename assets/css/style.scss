@charset "UTF-8";
@import "_variables";

.mieszkanie {
    &.fade-in {
        opacity: 0;
        animation: fadeIn 0.3s forwards;
    }

    &.fade-out {
        opacity: 1;
        animation: fadeOut 0.5s forwards;
        pointer-events: none;
    }
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

.leaflet-rrose-close-button,
.da-address-wrapper {
    display: none;
}

.layout-tooltip .leaflet-container {
    overflow: hidden !important;
}

.leaflet-rrose-content {
    width: 300px !important;
    margin: 10px !important;

    .mieszkanie-tooltip {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 10px;

        .row {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            align-items: center;

            &:last-of-type {
                .list {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;

                    font-weight: 600;

                    li:first-letter {
                        text-transform: capitalize;
                    }
                }
            }
        }

        .title:first-letter {
            text-transform: capitalize;
        }
    }

    .button {
        gap: 10px;
        text-transform: uppercase;
        font-size: $font-size-16px;

        .arrow {
            max-width: 50px;
            margin: 0;
        }
    }
}

.mieszkanie-single {
    padding-bottom: 7rem;

    .left-right {
        gap: 40px;
        .left {
            display: flex;
            flex-direction: column;
            flex-basis: calc(60% - 15px);
            gap: 30px;

            .main-image {
                display: flex;
                position: relative;
                height: 100%;

                img {
                    display: block;
                    max-height: 550px;
                    max-width: 550px;
                    margin: auto;
                    object-fit: contain;
                    aspect-ratio: 1 / 1;
                    width: 100%;
                    padding: 3rem 0;
                    transition: opacity 0.3s ease-out, transform 0.3s ease-out;

                    &.fade-out {
                        opacity: 0;
                        transform: scale(0.8);
                    }

                    &.placeholder {
                        display: block;
                        margin: auto;
                        max-width: 250px;
                    }
                }

                .placeholder-text {
                    display: flex;
                    justify-content: center;
                    position: absolute;
                    width: 100%;
                    bottom: 25%;
                    font-weight: 700;
                    text-align: center;
                    text-transform: uppercase;
                }
            }

            .gallery {
                display: flex;
                gap: 30px;

                .walkaround,
                .thumbnail {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 1rem;
                    flex: 1 1 0;
                    cursor: pointer;
                    border-radius: 4px;
                    gap: 20px;

                    .title {
                        font-weight: 700;
                        font-size: $font-size-16px;
                        text-align: center;
                        text-transform: uppercase;
                    }
                }

                .thumbnail {
                    img {
                        max-width: 100px;
                        object-fit: contain;
                        aspect-ratio: 1 / 1;
                    }
                }

                .walkaround {
                    &.empty {
                        justify-content: center;
                        cursor: unset;
                    }
                    img {
                        width: 100px;
                    }
                }
            }
        }
        .right {
            flex: 1;

            .info {
                padding: 20px;
                font-weight: 400;

                .row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    position: relative;
                    padding: 2rem 0;

                    .column {
                        display: flex;
                        flex-direction: column;
                        text-align: right;
                    }

                    &:after {
                        content: "";
                        position: absolute;
                        width: 100%;
                        height: 0.5px;
                        background: #897f7b;
                        top: 100%;
                        left: 0;
                    }

                    &:last-of-type {
                        strong {
                            text-transform: capitalize;
                            max-width: 151px;
                        }
                    }
                }
            }

            .wrapper {
                display: flex;
                gap: 20px;
                align-items: center;

                .link-pdf,
                #show-price-history {
                    flex-basis: calc(50% - 5px);
                    margin-top: 1.5rem;
                    white-space: nowrap;
                    font-size: $font-size-16px;
                }

                #show-price-history {
                    padding: 13px 10px;
                }
            }

            .link-pdf {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 10px;
                width: 100%;
                padding: 10px 15px;
                text-decoration: none;
                margin-top: 1.5rem;
                border-radius: 4px;
                font-weight: 700;
                font-size: $font-size-16px;
                text-transform: uppercase;
                text-align: center;
            }

            .dd_add-to-clipboard {
                position: relative;
                right: 0;
            }
        }
    }
}

#mieszkania-clipboard {
    .dd_clipboard-title {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;

        .column {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
        }

        button {
            background: none;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            cursor: pointer;
        }
    }

    #dd_clipboard-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        padding: 3rem 0;
        gap: 20px;

        .mieszkanie {
            margin-bottom: 1rem;
            padding: 3rem 1rem 1rem 1rem;

            button {
                bottom: 15px;
            }
        }
    }
}

.dd_clipboard-button {
    position: relative;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: $transition;

    &:hover,
    &:focus,
    &:active {
        transition: $transition;
    }

    .clipboard-status {
        position: absolute;
        top: 6px;
        right: 4px;
        font-size: 14px;
        font-weight: 800;
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }
}

.mieszkania-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .mieszkanie {
        margin-bottom: 3rem;
    }
}

// Swiper
.home {
    .mieszkaniaSwiper {
        .swiper-wrapper {
            .mieszkanie {
                margin-bottom: 3rem;
            }
        }
    }
}

.mieszkaniaSwiper {
    .swiper-wrapper {
        padding: 5rem 0 3rem 0;
        gap: 10px;

        .mieszkanie {
            padding: 1.25rem;

            .content {
                .top {
                    ul,
                    ol,
                    li {
                        padding-inline-start: 0 !important;
                    }
                }
            }

            .dd_add-to-clipboard {
                bottom: 1.5rem !important;
            }
        }
    }
}
// Swiper

.mieszkaniaSwiper,
#dd_clipboard-list,
.mieszkania-list {
    .mieszkanie {
        display: flex;
        position: relative;
        flex-basis: calc(25% - 15px);
        height: auto;

        .property-link {
            display: flex;
            flex-direction: column;
            width: 100%;
            color: #4d4c46;

            img {
                display: block;
                margin: auto;
                padding-bottom: 1rem;
                height: 210px;

                &.placeholder {
                    width: 35%;
                    min-height: 200px;
                    object-fit: contain;
                    aspect-ratio: 1 / 1;
                }
            }

            &a:hover {
                color: unset;
            }

            .title {
                font-weight: 700;
                line-height: 2.5rem;
            }

            .content {
                display: flex;
                flex-direction: column;
                gap: 10px;
                height: 100%;

                &.with-rooms {
                    min-height: 125px;
                }

                .top {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;

                    li {
                        padding-left: 0;
                    }

                    .info {
                        display: flex;
                        flex-direction: column;
                        align-items: start;
                        flex-wrap: wrap;
                        gap: 5px;
                        max-width: 60%;

                        div {
                            display: flex;
                            align-items: start;
                            gap: 7px;
                        }
                    }

                    .status {
                        font-weight: 700;
                        font-size: $font-size-16px;
                        white-space: nowrap;
                    }
                }
            }

            .bottom {
                display: flex;
                margin-top: auto;

                .price {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                    width: 75%;

                    .price-value {
                        font-weight: 700;
                        font-size: $font-size-24px;
                        line-height: 25px;
                    }
                }
            }
        }
    }
}

.dd_add-to-clipboard,
.dd_remove-from-clipboard {
    position: absolute;
    bottom: 0px;
    right: 15px;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;

    &.added {
        transition: $transition;
        background: #4caf50 !important;
    }
}

.swiper-button-next,
.swiper-button-prev {
    background-color: transparent;
    border: none;
    top: 25px !important;
    position: absolute;
    visibility: hidden;
    width: fit-content !important;

    &:after {
        padding: 5px;
        display: block;
    }
}

.swiper-button-prev {
    &::after {
        background-image: url(../images/arrow-left.svg);
        background-repeat: no-repeat;
        width: 35px;
        height: 35px;
        content: "" !important;
    }
}

.swiper-button-next {
    &::after {
        background-image: url(../images/arrow-right.svg);
        background-repeat: no-repeat;
        width: 35px;
        height: 35px;
        content: "" !important;
    }
}

.fade-in {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.status {
    &.sprzedane {
        color: #c53521;
    }

    &.dostepne {
        color: #38894a;
    }

    &.rezerwacja {
        color: #d79729;
    }
}

.hotspot-dostepne {
    fill: #38894a;
    stroke-width: 1;
    fill-opacity: 0.41;
    stroke: #ffffff;
    stroke-opacity: 0.81;
    transition: $transition;
}

.hotspot-sprzedane {
    fill: #c53521;
    stroke-width: 1;
    fill-opacity: 0.41;
    stroke: #ffffff;
    stroke-opacity: 0.81;
    transition: $transition;
}

.hotspot-rezerwacja {
    fill: #d79729;
    stroke-width: 1;
    fill-opacity: 0.41;
    stroke: #ffffff;
    stroke-opacity: 0.81;
    transition: $transition;
}

.hotspot-dostepne,
.hotspot-sprzedane,
.hotspot-zarezerwowane {
    &:hover {
        fill: #ffffff;
        fill-opacity: 0.81;
        outline: none;
        stroke: #ffffff;
        stroke-opacity: 0.81;
        transition: $transition;
    }
}

#main-content {
    .button {
        &.reverse {
            &.back {
                gap: 10px;
                width: fit-content;
            }
        }
    }
}

.dd-price-history-modal-overlay {
    position: fixed;
    inset: 0;
    z-index: 1201;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;

    &.is-open {
        opacity: 1;
        visibility: visible;

        .dd-price-history-modal {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .dd-price-history-modal {
        padding: 24px;
        max-width: 65%;
        width: 65%;
        position: relative;
        overflow-y: visible;
        max-height: none;
        transform: translateY(20px);
        opacity: 0;
        transition: transform 0.3s ease, opacity 0.3s ease;

        .table-wrapper {
            table {
                width: 100%;
                border-collapse: collapse;
                font-size: $font-size-18px;

                thead th,
                tbody td {
                    padding: 20px 8px;

                    &:first-of-type {
                        text-align: left;
                        padding: 20px 20px 20px 20px;
                    }
                }

                thead {
                    th {
                        font-weight: 700;
                        border-bottom: 1px solid #9e9e9e;
                        text-align: center;
                    }
                }

                tbody {
                    &:before {
                        content: "@";
                        display: block;
                        line-height: 10px;
                        text-indent: -99999px;
                    }

                    tr {
                        &:first-of-type {
                            font-weight: 700;
                        }
                    }

                    td {
                        text-align: center;
                    }
                }
            }
        }

        .dd-price-history-modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: transparent;
            border: none;
            cursor: pointer;
            width: 30px;
            height: 30px;
            padding: 0;

            svg {
                width: 100%;
                height: 100%;
            }
        }
    }
}

@media (max-width: $media-hd) {
}
@media (max-width: $media-laptop) {
}
@media (max-width: $media-tablet) {
    .mieszkanie-single {
        padding: 5rem 0;
        .left-right {
            flex-direction: column;
            flex-wrap: nowrap;

            .left {
                flex-basis: 100%;

                .main-image {
                    min-height: unset;
                    flex-direction: column;
                    gap: 10px;

                    img {
                        max-width: 100%;
                        max-height: 400px;
                        padding: 0;

                        &.placeholder {
                            max-width: 200px;
                        }
                    }

                    .placeholder-text {
                        position: relative;
                    }
                }

                .gallery {
                    gap: 15px;
                    white-space: nowrap;
                    overflow-x: auto;
                    overflow-y: hidden;

                    .walkaround,
                    .thumbnail {
                        padding: 1rem;
                        min-width: 42%;
                        gap: 10px;

                        .title {
                            white-space: pre-wrap;
                            text-align: center;
                            font-size: $font-size-14px;
                            word-break: break-word;
                        }

                        img {
                            white-space: nowrap;
                            max-width: 60px;
                        }
                    }
                    .walkaround {
                        &.empty {
                            .title {
                                max-width: 160px;
                                font-size: 12px;
                            }
                        }
                    }
                }
            }

            .right {
                .info {
                    padding: 0;
                }

                .wrapper {
                    flex-wrap: wrap;

                    #show-price-history {
                        margin: 0;
                    }

                    .link-pdf,
                    #show-price-history {
                        flex-basis: 100%;
                    }
                }

                .link-pdf {
                    margin-top: 20px;
                }
            }
        }
    }

    .mieszkaniaSwiper {
        overflow: hidden;
        .swiper-wrapper {
            padding: 3rem 0;

            .mieszkanie {
                margin-top: 3rem;
                padding: 0.5rem;
            }
        }

        .swiper-button-prev,
        .swiper-button-next {
            visibility: visible;
        }

        .swiper-button-prev {
            left: var(--swiper-navigation-sides-offset, 40%) !important;
        }

        .swiper-button-next {
            right: var(--swiper-navigation-sides-offset, 40%) !important;
        }
    }

    #mieszkania-clipboard {
        #dd_clipboard-list {
            .mieszkanie {
                padding: 1rem;
                margin-bottom: unset;
            }
        }
    }

    #dd_clipboard-list,
    .mieszkania-list,
    .mieszkaniaSwiper {
        padding: 3rem 0;
        .mieszkanie {
            flex-basis: calc(50% - 10px);
        }
    }

    .dd-price-history-modal-overlay {
        &.is-open {
            .dd-price-history-modal {
                transform: translateY(50px);
            }
        }

        .dd-price-history-modal {
            max-width: 100%;
            width: 100%;
            overflow-y: auto;

            .modal-title {
                padding-left: 0;
            }

            .table-wrapper {
                overflow: auto;
                max-height: 90vh;
                margin-top: 1rem;
                padding-bottom: 0;

                table {


                    thead th,
                    tbody td {
                        padding: 20px;
                        white-space: nowrap;

                        &:first-of-type {
                            padding: 20px 20px 20px 0px;
                        }
                    }
                }
            }

            .dd-price-history-modal-close {
                width: 25px;
                height: 25px;
            }
        }
    }
}

@media (max-width: $media-mobile) {
    .leaflet-rrose {
        transform: translate3d(0%, 25px, 0px) !important;
    }

    .mieszkanie-single {
        padding: 3rem 0;
    }

    #dd_clipboard-list,
    .mieszkania-list,
    .mieszkaniaSwiper {
        .mieszkanie {
            flex-basis: 100%;
        }
    }

    .mieszkania-list {
        .mieszkanie {
            margin-bottom: 1.5rem;
        }
    }

    .mieszkaniaSwiper {
        .swiper-wrapper {
            padding-bottom: 0;
        }
    }

    .mieszkaniaSwiper {
        .swiper-button-prev {
            left: var(--swiper-navigation-sides-offset, 35%) !important;
        }

        .swiper-button-next {
            right: var(--swiper-navigation-sides-offset, 35%) !important;
        }
    }
}
@media (max-width: $media-mobile-sm) {
    .leaflet-rrose {
        transform: translate3d(25%, 0px, 0px) !important;

        .leaflet-rrose-content {
            max-width: fit-content !important;
            width: fit-content !important;

            .mieszkanie-tooltip {
                .row {
                    flex-wrap: wrap;
                }
            }
        }
    }

    .mieszkaniaSwiper {
        .swiper-button-prev {
            left: var(--swiper-navigation-sides-offset, 25%) !important;
        }

        .swiper-button-next {
            right: var(--swiper-navigation-sides-offset, 25%) !important;
        }
    }
}

.dd-3d-tour-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    z-index: 9999;
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
}

.dd-3d-tour-modal-overlay.is-open {
    display: flex;
}

.dd-3d-tour-modal {
    background: white;
    width: 90%;
    height: 90vh;
    border-radius: 12px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.dd-3d-tour-modal-close svg {
    position: absolute;
    top: 15px;
    right: 15px;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.dd-3d-tour-modal-close:hover {
    background: #e0e0e0;
}

.dd-3d-tour-modal-iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 12px;
}

.real-estate-filters {
    margin: 1rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;

    .filter-form {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;

        @media (max-width: $media-mobile) {
            flex-direction: column;
            align-items: stretch;
        }
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        @media (max-width: $media-mobile) {
            flex-direction: column;
            align-items: stretch;
        }

        label {
            font-weight: 600;
            white-space: nowrap;
        }

        select {
            padding: 0.5rem;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
            font-size: $font-size-14px;
            min-width: 200px;

            &:focus {
                outline-offset: 2px;
            }
        }
    }
}

.real-estate-table-container {
    margin: 2rem 0;
    overflow-x: auto;
    border-radius: 4px;
    background: white;
    border: 2px solid #e9ecef;

    @media (max-width: $media-mobile) {
        margin: 1rem 0;
        border-radius: 4px;
    }
}

.real-estate-table {
    width: 100%;
    border-collapse: collapse;
    font-size: $font-size-16px;
    min-width: 600px;

    @media (max-width: $media-mobile) {
        font-size: $font-size-14px;
        min-width: 500px;
    }

    thead {
        background: #f8f9fa;

        th {
            padding: 1rem 0.75rem;
            text-align: left;
            font-weight: 700;
            border-bottom: 2px solid #e9ecef;
            font-size: $font-size-16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            @media (max-width: $media-mobile) {
                padding: 0.75rem 0.5rem;
                font-size: $font-size-14px;
            }

            &:first-child {
                border-top-left-radius: 8px;
            }

            &:last-child {
                border-top-right-radius: 8px;
            }

            &.sortable-header {
                a {
                    text-decoration: none;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    transition: color 0.2s ease;

                    &:hover {
                        color: #2d5a39;
                    }

                    .sort-arrow {
                        font-size: 0.8em;
                        margin-left: 0.5rem;
                        opacity: 0.7;
                        transition: opacity 0.2s ease;
                    }

                    &:hover .sort-arrow {
                        opacity: 1;
                    }
                }
            }
        }
    }

    tbody {
        tr {
            transition: background-color 0.2s ease;
            border-bottom: 1px solid #e9ecef;

            &:hover {
                background-color: #f8f9fa;
            }

            &:last-child {
                border-bottom: none;
            }
        }

        td {
            padding: 1rem 0.75rem;
            vertical-align: middle;
            color: #4d4c46;

            @media (max-width: $media-mobile) {
                padding: 0.75rem 0.5rem;
            }

            &:first-child {
                font-weight: 600;
            }
        }
    }

    .price-container {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;

        .main-price {
            font-weight: 700;
            font-size: $font-size-18px;

            @media (max-width: $media-mobile) {
                font-size: $font-size-16px;
            }
        }

        .price-per-meter {
            font-size: $font-size-14px;
            color: #6c757d;
            font-weight: 500;

            @media (max-width: $media-mobile) {
                font-size: 12px;
            }
        }
    }

    .details-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        font-weight: 600;
        font-size: $font-size-14px;
        transition: all 0.2s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        @media (max-width: $media-mobile) {
            padding: 0.375rem 0.75rem;
            font-size: 12px;
        }

        &:hover {
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        &:focus {
            outline-offset: 2px;
        }
    }
}
