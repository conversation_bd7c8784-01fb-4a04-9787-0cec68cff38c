// Price tooltip styles
.price-tooltip-container {
  position: relative;
  display: inline-block;
  margin-left: 8px;
  
  .price-tooltip-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: $color-primary;
    color: $color-white;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
    
    &:hover {
      background-color: $color-button-hover;
    }
  }
  
  .price-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 8px;
    padding: 12px 16px;
    background-color: $color-black;
    color: $color-white;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.4;
    white-space: nowrap;
    max-width: 300px;
    white-space: normal;
    width: max-content;
    max-width: 280px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    
    // Arrow pointing down
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 6px solid transparent;
      border-top-color: $color-black;
    }
    
    a {
      color: $color-hover;
      text-decoration: underline;
      
      &:hover {
        color: $color-white;
      }
    }
  }
  
  &:hover .price-tooltip {
    opacity: 1;
    visibility: visible;
  }
}

// Responsive styles for tooltip
@media (max-width: $media-tablet) {
  .price-tooltip-container {
    .price-tooltip {
      max-width: 250px;
      font-size: 13px;
      padding: 10px 14px;
      
      // Adjust position for mobile
      left: auto;
      right: 0;
      transform: none;
      
      &::after {
        left: auto;
        right: 20px;
        transform: none;
      }
    }
  }
}

@media (max-width: $media-mobile) {
  .price-tooltip-container {
    .price-tooltip {
      max-width: 200px;
      font-size: 12px;
      padding: 8px 12px;
    }
  }
}
