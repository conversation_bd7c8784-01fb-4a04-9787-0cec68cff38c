<?php

namespace DD\App\Mieszkania;

use DD\App\DTO\RealEstate;
use DD\App\Enum\SortDirection;
use DD\App\Enum\SortField;
use DD\App\Enum\Type;
use DD\App\Repository\RealEstateRepository;
use ValueError;

class RealEstateListView
{
    public static function render(): void
    {
        if (is_admin() || wp_is_json_request()) {
            return;
        }

        $realEstateRepository = new RealEstateRepository();

        $sortField = null;
        $sortDirection = null;
        $filterType = null;

        if (isset($_GET['sort']) && $_GET['sort'] !== '') {
            try {
                $sortField = SortField::from($_GET['sort']);
            } catch (ValueError $e) {
            }
        }

        if (isset($_GET['direction']) && $_GET['direction'] !== '') {
            try {
                $sortDirection = SortDirection::from($_GET['direction']);
            } catch (ValueError $e) {
            }
        }

        if (isset($_GET['type']) && $_GET['type'] !== '') {
            try {
                $filterType = Type::from($_GET['type']);
            } catch (ValueError $e) {
            }
        }

        if ($sortField || $filterType) {
            $realEstates = $realEstateRepository->getRealEstatesWithSortingAndFiltering($sortField, $sortDirection, $filterType);
        } else {
            $realEstates = $realEstateRepository->getAllRealEstates();
        }

        self::renderContent($realEstates, $sortField, $sortDirection, $filterType);
    }

    /**
     * @param array<RealEstate> $realEstates
     */
    private static function renderContent(array $realEstates, ?SortField $currentSortField = null, ?SortDirection $currentSortDirection = null, ?Type $currentFilterType = null): void
    {
        self::renderFilterForm($currentFilterType);

        if (empty($realEstates)) {
            echo '<p>Brak dostępnych lokali.</p>';
            return;
        }

        echo '<div class="real-estate-table-container">';
        echo '<table class="real-estate-table">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>Lokal</th>';
        echo self::renderSortableHeader('Powierzchnia', SortField::AREA, $currentSortField, $currentSortDirection, $currentFilterType);
        echo self::renderSortableHeader('Cena brutto', SortField::PRICE, $currentSortField, $currentSortDirection, $currentFilterType);
        echo '<th>Status</th>';
        echo '<th>Szczegóły</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($realEstates as $realEstate) {
            echo '<tr>';

            // Lokal
            echo '<td>' . esc_html($realEstate->type->displayName()) . ' ' . esc_html($realEstate->localNumber) . '</td>';

            // Powierzchnia
            if ($realEstate->area > 0) {
                echo '<td>' . number_format($realEstate->area, 2, ',', ' ') . ' m² ' . '</td>';
            } else {
                echo '<td>-</td>';
            }

            // Cena brutto z ceną za metr
            echo '<td>';
            if ($realEstate->price > 0) {
                echo '<div class="price-container">';
                echo '<div class="main-price">' . number_format($realEstate->price, 0, ',', ' ') . ' zł</div>';
                if ($realEstate->pricePerMeter > 0) {
                    echo '<div class="price-per-meter">' . number_format($realEstate->pricePerMeter, 0, ',', ' ') . ' zł / m²</div>';
                }
                echo '</div>';
            } else {
                echo '-';
            }
            echo '</td>';

            // Status
            echo '<td>';
            echo '<span class="status ' . esc_attr($realEstate->status->value) . '">';
            echo esc_html($realEstate->status->displayName());
            echo '</span>';
            echo '</td>';

            // Link do szczegółów
            echo '<td>';
            if ($realEstate->postId) {
                $permalink = get_permalink($realEstate->postId);
                if ($permalink) {
                    echo '<a href="' . esc_url($permalink) . '" class="details-link">Zobacz szczegóły</a>';
                } else {
                    echo '-';
                }
            } else {
                echo '-';
            }
            echo '</td>';

            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
        echo '</div>';
    }

    private static function renderFilterForm(?Type $currentFilterType): void
    {
        echo '<div class="real-estate-filters">';
        echo '<form method="get" class="filter-form">';

        if (isset($_GET['sort'])) {
            echo '<input type="hidden" name="sort" value="' . esc_attr($_GET['sort']) . '">';
        }
        if (isset($_GET['direction'])) {
            echo '<input type="hidden" name="direction" value="' . esc_attr($_GET['direction']) . '">';
        }

        echo '<div class="filter-group">';
        echo '<label for="type-filter">Filtruj po typie nieruchomości:</label>';
        echo '<select name="type" id="type-filter" onchange="this.form.submit()">';
        echo '<option value="">Wszystkie typy</option>';

        foreach (Type::cases() as $type) {
            $selected = $currentFilterType === $type ? 'selected' : '';
            echo '<option value="' . esc_attr($type->value) . '" ' . $selected . '>';
            echo esc_html($type->displayName());
            echo '</option>';
        }

        echo '</select>';
        echo '</div>';
        echo '</form>';
        echo '</div>';
    }

    private static function renderSortableHeader(string $title, SortField $field, ?SortField $currentSortField, ?SortDirection $currentSortDirection, ?Type $currentFilterType): string
    {
        $isCurrentField = $currentSortField === $field;
        $nextDirection = $isCurrentField && $currentSortDirection === SortDirection::ASC ? SortDirection::DESC : SortDirection::ASC;

        $params = [
            'sort' => $field->value,
            'direction' => $nextDirection->value
        ];

        if ($currentFilterType) {
            $params['type'] = $currentFilterType->value;
        }

        $url = add_query_arg($params, remove_query_arg(['sort', 'direction', 'type']));

        $arrow = '';
        if ($isCurrentField) {
            $arrow = $currentSortDirection === SortDirection::ASC ? ' ▲' : ' ▼';
        } else {
            $arrow = ' ▲▼';
        }

        return '<th class="sortable-header"><a href="' . esc_url($url) . '">' . esc_html($title) . '<span class="sort-arrow">' . $arrow . '</span></a></th>';
    }
}